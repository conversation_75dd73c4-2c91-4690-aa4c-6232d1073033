import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

namespace ErpPurchaseReturnApi {
  /** 采购退货信息 */
  export interface PurchaseReturn {
    id?: number; // 采购退货编号
    no: string; // 采购退货号
    supplierId: number; // 供应商编号
    returnTime: Date; // 退货时间
    totalCount: number; // 合计数量
    totalPrice: number; // 合计金额，单位：元
    status: number; // 状态
    remark: string; // 备注
  }

  /** 采购退货分页查询参数 */
  export interface PurchaseReturnPageParams extends PageParam {
    no?: string;
    supplierId?: number;
    status?: number;
  }

  /** 采购退货状态更新参数 */
  export interface PurchaseReturnStatusParams {
    id: number;
    status: number;
  }
}

/**
 * 查询采购退货分页
 */
export function getPurchaseReturnPage(
  params: ErpPurchaseReturnApi.PurchaseReturnPageParams,
) {
  return requestClient.get<PageResult<ErpPurchaseReturnApi.PurchaseReturn>>(
    '/erp/purchase-return/page',
    {
      params,
    },
  );
}

/**
 * 查询采购退货详情
 */
export function getPurchaseReturn(id: number) {
  return requestClient.get<ErpPurchaseReturnApi.PurchaseReturn>(
    `/erp/purchase-return/get?id=${id}`,
  );
}

/**
 * 新增采购退货
 */
export function createPurchaseReturn(
  data: ErpPurchaseReturnApi.PurchaseReturn,
) {
  return requestClient.post('/erp/purchase-return/create', data);
}

/**
 * 修改采购退货
 */
export function updatePurchaseReturn(
  data: ErpPurchaseReturnApi.PurchaseReturn,
) {
  return requestClient.put('/erp/purchase-return/update', data);
}

/**
 * 更新采购退货的状态
 */
export function updatePurchaseReturnStatus(
  params: ErpPurchaseReturnApi.PurchaseReturnStatusParams,
) {
  return requestClient.put('/erp/purchase-return/update-status', null, {
    params,
  });
}

/**
 * 删除采购退货
 */
export function deletePurchaseReturn(ids: number[]) {
  return requestClient.delete('/erp/purchase-return/delete', {
    params: {
      ids: ids.join(','),
    },
  });
}

/**
 * 导出采购退货 Excel
 */
export function exportPurchaseReturn(
  params: ErpPurchaseReturnApi.PurchaseReturnPageParams,
) {
  return requestClient.download('/erp/purchase-return/export-excel', {
    params,
  });
}
