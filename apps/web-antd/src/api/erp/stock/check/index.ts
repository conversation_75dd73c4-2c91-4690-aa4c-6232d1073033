import type { PageParam, PageResult } from '@vben/request';

import { requestClient } from '#/api/request';

namespace ErpStockCheckApi {
  /** 库存盘点单信息 */
  export interface StockCheck {
    id?: number; // 盘点编号
    no: string; // 盘点单号
    checkTime: Date; // 盘点时间
    totalCount: number; // 合计数量
    totalPrice: number; // 合计金额，单位：元
    status: number; // 状态
    remark: string; // 备注
  }

  /** 库存盘点单分页查询参数 */
  export interface StockCheckPageParams extends PageParam {
    no?: string;
    status?: number;
  }

  /** 库存盘点单状态更新参数 */
  export interface StockCheckStatusParams {
    id: number;
    status: number;
  }
}

/**
 * 查询库存盘点单分页
 */
export function getStockCheckPage(
  params: ErpStockCheckApi.StockCheckPageParams,
) {
  return requestClient.get<PageResult<ErpStockCheckApi.StockCheck>>(
    '/erp/stock-check/page',
    {
      params,
    },
  );
}

/**
 * 查询库存盘点单详情
 */
export function getStockCheck(id: number) {
  return requestClient.get<ErpStockCheckApi.StockCheck>(
    `/erp/stock-check/get?id=${id}`,
  );
}

/**
 * 新增库存盘点单
 */
export function createStockCheck(data: ErpStockCheckApi.StockCheck) {
  return requestClient.post('/erp/stock-check/create', data);
}

/**
 * 修改库存盘点单
 */
export function updateStockCheck(data: ErpStockCheckApi.StockCheck) {
  return requestClient.put('/erp/stock-check/update', data);
}

/**
 * 更新库存盘点单的状态
 */
export function updateStockCheckStatus(
  params: ErpStockCheckApi.StockCheckStatusParams,
) {
  return requestClient.put('/erp/stock-check/update-status', null, {
    params,
  });
}

/**
 * 删除库存盘点单
 */
export function deleteStockCheck(ids: number[]) {
  return requestClient.delete('/erp/stock-check/delete', {
    params: {
      ids: ids.join(','),
    },
  });
}

/**
 * 导出库存盘点单 Excel
 */
export function exportStockCheck(
  params: ErpStockCheckApi.StockCheckPageParams,
) {
  return requestClient.download('/erp/stock-check/export-excel', {
    params,
  });
}
