.svelte-flow {
  --xy-edge-stroke-default: #b1b1b7;
  --xy-edge-stroke-width-default: 1;
  --xy-edge-stroke-selected-default: #555;
  --xy-connectionline-stroke-default: #b1b1b7;
  --xy-connectionline-stroke-width-default: 1;
  --xy-attribution-background-color-default: rgb(255 255 255 / 50%);
  --xy-minimap-background-color-default: #fff;
  --xy-minimap-mask-background-color-default: rgb(240 240 240 / 60%);
  --xy-minimap-mask-stroke-color-default: transparent;
  --xy-minimap-mask-stroke-width-default: 1;
  --xy-minimap-node-background-color-default: #e2e2e2;
  --xy-minimap-node-stroke-color-default: transparent;
  --xy-minimap-node-stroke-width-default: 2;
  --xy-background-color-default: transparent;
  --xy-background-pattern-dots-color-default: #91919a;
  --xy-background-pattern-lines-color-default: #eee;
  --xy-background-pattern-cross-color-default: #e2e2e2;
  --xy-node-color-default: inherit;
  --xy-node-border-default: 1px solid #1a192b;
  --xy-node-background-color-default: #fff;
  --xy-node-group-background-color-default: rgb(240 240 240 / 25%);
  --xy-node-boxshadow-hover-default: 0 1px 4px 1px rgb(0 0 0 / 8%);
  --xy-node-boxshadow-selected-default: 0 0 0 0.5px #1a192b;
  --xy-node-border-radius-default: 3px;
  --xy-handle-background-color-default: #1a192b;
  --xy-handle-border-color-default: #fff;
  --xy-selection-background-color-default: rgb(0 89 220 / 8%);
  --xy-selection-border-default: 1px dotted rgb(0 89 220 / 80%);
  --xy-controls-button-background-color-default: #fefefe;
  --xy-controls-button-background-color-hover-default: #f4f4f4;
  --xy-controls-button-color-default: inherit;
  --xy-controls-button-color-hover-default: inherit;
  --xy-controls-button-border-color-default: #eee;
  --xy-controls-box-shadow-default: 0 0 2px 1px rgb(0 0 0 / 8%);
  --xy-edge-label-background-color-default: #fff;
  --xy-edge-label-color-default: inherit;
  --xy-resize-background-color-default: #3367d9;

  direction: ltr;
  background-color: var(
    --xy-background-color,
    var(--xy-background-color-default)
  );
}

.svelte-flow.dark {
  --xy-edge-stroke-default: #3e3e3e;
  --xy-edge-stroke-width-default: 1;
  --xy-edge-stroke-selected-default: #727272;
  --xy-connectionline-stroke-default: #b1b1b7;
  --xy-connectionline-stroke-width-default: 1;
  --xy-attribution-background-color-default: rgb(150 150 150 / 25%);
  --xy-minimap-background-color-default: #141414;
  --xy-minimap-mask-background-color-default: rgb(60 60 60 / 60%);
  --xy-minimap-mask-stroke-color-default: transparent;
  --xy-minimap-mask-stroke-width-default: 1;
  --xy-minimap-node-background-color-default: #2b2b2b;
  --xy-minimap-node-stroke-color-default: transparent;
  --xy-minimap-node-stroke-width-default: 2;
  --xy-background-color-default: #141414;
  --xy-background-pattern-dots-color-default: #777;
  --xy-background-pattern-lines-color-default: #777;
  --xy-background-pattern-cross-color-default: #777;
  --xy-node-color-default: #f8f8f8;
  --xy-node-border-default: 1px solid #3c3c3c;
  --xy-node-background-color-default: #1e1e1e;
  --xy-node-group-background-color-default: rgb(240 240 240 / 25%);
  --xy-node-boxshadow-hover-default: 0 1px 4px 1px rgb(255 255 255 / 8%);
  --xy-node-boxshadow-selected-default: 0 0 0 0.5px #999;
  --xy-handle-background-color-default: #bebebe;
  --xy-handle-border-color-default: #1e1e1e;
  --xy-selection-background-color-default: rgb(200 200 220 / 8%);
  --xy-selection-border-default: 1px dotted rgb(200 200 220 / 80%);
  --xy-controls-button-background-color-default: #2b2b2b;
  --xy-controls-button-background-color-hover-default: #3e3e3e;
  --xy-controls-button-color-default: #f8f8f8;
  --xy-controls-button-color-hover-default: #fff;
  --xy-controls-button-border-color-default: #5b5b5b;
  --xy-controls-box-shadow-default: 0 0 2px 1px rgb(0 0 0 / 8%);
  --xy-edge-label-background-color-default: #141414;
  --xy-edge-label-color-default: #f8f8f8;
}

.svelte-flow__background {
  z-index: -1;
  pointer-events: none;
  background-color: var(
    --xy-background-color,
    var(--xy-background-color-props, var(--xy-background-color-default))
  );
}

.svelte-flow__container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.svelte-flow__pane {
  z-index: 1;
}

.svelte-flow__pane.draggable {
  cursor: grab;
}

.svelte-flow__pane.dragging {
  cursor: grabbing;
}

.svelte-flow__pane.selection {
  cursor: pointer;
}

.svelte-flow__viewport {
  z-index: 2;
  pointer-events: none;
  transform-origin: 0 0;
}

.svelte-flow__renderer {
  z-index: 4;
}

.svelte-flow__selection {
  z-index: 6;
}

.svelte-flow__nodesselection-rect:focus,
.svelte-flow__nodesselection-rect:focus-visible {
  outline: none;
}

.svelte-flow__edge-path {
  fill: none;
  stroke: var(--xy-edge-stroke, var(--xy-edge-stroke-default));
  stroke-width: var(
    --xy-edge-stroke-width,
    var(--xy-edge-stroke-width-default)
  );
}

.svelte-flow__connection-path {
  fill: none;
  stroke: var(
    --xy-connectionline-stroke,
    var(--xy-connectionline-stroke-default)
  );
  stroke-width: var(
    --xy-connectionline-stroke-width,
    var(--xy-connectionline-stroke-width-default)
  );
}

.svelte-flow .svelte-flow__edges {
  position: absolute;
}

.svelte-flow .svelte-flow__edges svg {
  position: absolute;
  overflow: visible;
  pointer-events: none;
}

.svelte-flow__edge {
  pointer-events: visiblestroke;
}

.svelte-flow__edge.selectable {
  cursor: pointer;
}

.svelte-flow__edge.animated path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

.svelte-flow__edge.animated path.svelte-flow__edge-interaction {
  stroke-dasharray: none;
  animation: none;
}

.svelte-flow__edge.inactive {
  pointer-events: none;
}

.svelte-flow__edge.selected,
.svelte-flow__edge:focus,
.svelte-flow__edge:focus-visible {
  outline: none;
}

.svelte-flow__edge.selected .svelte-flow__edge-path,
.svelte-flow__edge.selectable:focus .svelte-flow__edge-path,
.svelte-flow__edge.selectable:focus-visible .svelte-flow__edge-path {
  stroke: var(
    --xy-edge-stroke-selected,
    var(--xy-edge-stroke-selected-default)
  );
}

.svelte-flow__edge-textwrapper {
  pointer-events: all;
}

.svelte-flow__edge .svelte-flow__edge-text {
  pointer-events: none;
  user-select: none;
}

.svelte-flow__connection {
  pointer-events: none;
}

.svelte-flow__connection .animated {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

svg.svelte-flow__connectionline {
  position: absolute;
  z-index: 1001;
  overflow: visible;
}

.svelte-flow__nodes {
  pointer-events: none;
  transform-origin: 0 0;
}

.svelte-flow__node {
  position: absolute;
  box-sizing: border-box;
  pointer-events: all;
  cursor: default;
  user-select: none;
  transform-origin: 0 0;
}

.svelte-flow__node.selectable {
  cursor: pointer;
}

.svelte-flow__node.draggable {
  pointer-events: all;
  cursor: grab;
}

.svelte-flow__node.draggable.dragging {
  cursor: grabbing;
}

.svelte-flow__nodesselection {
  z-index: 3;
  pointer-events: none;
  transform-origin: left top;
}

.svelte-flow__nodesselection-rect {
  position: absolute;
  pointer-events: all;
  cursor: grab;
}

.svelte-flow__handle {
  position: absolute;
  width: 6px;
  min-width: 5px;
  height: 6px;
  min-height: 5px;
  pointer-events: none;
  background-color: var(
    --xy-handle-background-color,
    var(--xy-handle-background-color-default)
  );
  border: 1px solid
    var(--xy-handle-border-color, var(--xy-handle-border-color-default));
  border-radius: 100%;
}

.svelte-flow__handle.connectingfrom {
  pointer-events: all;
}

.svelte-flow__handle.connectionindicator {
  pointer-events: all;
  cursor: crosshair;
}

.svelte-flow__handle-bottom {
  top: auto;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 50%);
}

.svelte-flow__handle-top {
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);
}

.svelte-flow__handle-left {
  top: 50%;
  left: 0;
  transform: translate(-50%, -50%);
}

.svelte-flow__handle-right {
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
}

.svelte-flow__edgeupdater {
  pointer-events: all;
  cursor: move;
}

.svelte-flow__panel {
  position: absolute;
  z-index: 5;
  margin: 15px;
}

.svelte-flow__panel.top {
  top: 0;
}

.svelte-flow__panel.bottom {
  bottom: 0;
}

.svelte-flow__panel.left {
  left: 0;
}

.svelte-flow__panel.right {
  right: 0;
}

.svelte-flow__panel.center {
  left: 50%;
  transform: translate(-50%);
}

.svelte-flow__attribution {
  padding: 2px 3px;
  margin: 0;
  font-size: 10px;
  background: var(
    --xy-attribution-background-color,
    var(--xy-attribution-background-color-default)
  );
}

.svelte-flow__attribution a {
  color: #999;
  text-decoration: none;
}

@keyframes dashdraw {
  0% {
    stroke-dashoffset: 10;
  }
}

.svelte-flow__edgelabel-renderer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  user-select: none;
}

.svelte-flow__viewport-portal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  user-select: none;
}

.svelte-flow__minimap {
  background: var(
    --xy-minimap-background-color-props,
    var(
      --xy-minimap-background-color,
      var(--xy-minimap-background-color-default)
    )
  );
}

.svelte-flow__minimap-svg {
  display: block;
}

.svelte-flow__minimap-mask {
  fill: var(
    --xy-minimap-mask-background-color-props,
    var(
      --xy-minimap-mask-background-color,
      var(--xy-minimap-mask-background-color-default)
    )
  );
  stroke: var(
    --xy-minimap-mask-stroke-color-props,
    var(
      --xy-minimap-mask-stroke-color,
      var(--xy-minimap-mask-stroke-color-default)
    )
  );
  stroke-width: var(
    --xy-minimap-mask-stroke-width-props,
    var(
      --xy-minimap-mask-stroke-width,
      var(--xy-minimap-mask-stroke-width-default)
    )
  );
}

.svelte-flow__minimap-node {
  fill: var(
    --xy-minimap-node-background-color-props,
    var(
      --xy-minimap-node-background-color,
      var(--xy-minimap-node-background-color-default)
    )
  );
  stroke: var(
    --xy-minimap-node-stroke-color-props,
    var(
      --xy-minimap-node-stroke-color,
      var(--xy-minimap-node-stroke-color-default)
    )
  );
  stroke-width: var(
    --xy-minimap-node-stroke-width-props,
    var(
      --xy-minimap-node-stroke-width,
      var(--xy-minimap-node-stroke-width-default)
    )
  );
}

.svelte-flow__background-pattern.dots {
  fill: var(
    --xy-background-pattern-color-props,
    var(
      --xy-background-pattern-color,
      var(--xy-background-pattern-dots-color-default)
    )
  );
}

.svelte-flow__background-pattern.lines {
  stroke: var(
    --xy-background-pattern-color-props,
    var(
      --xy-background-pattern-color,
      var(--xy-background-pattern-lines-color-default)
    )
  );
}

.svelte-flow__background-pattern.cross {
  stroke: var(
    --xy-background-pattern-color-props,
    var(
      --xy-background-pattern-color,
      var(--xy-background-pattern-cross-color-default)
    )
  );
}

.svelte-flow__controls {
  display: flex;
  flex-direction: column;
  box-shadow: var(
    --xy-controls-box-shadow,
    var(--xy-controls-box-shadow-default)
  );
}

.svelte-flow__controls.horizontal {
  flex-direction: row;
}

.svelte-flow__controls-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  padding: 4px;
  color: var(
    --xy-controls-button-color-props,
    var(--xy-controls-button-color, var(--xy-controls-button-color-default))
  );
  cursor: pointer;
  user-select: none;
  background: var(
    --xy-controls-button-background-color,
    var(--xy-controls-button-background-color-default)
  );
  border: none;
  border-bottom: 1px solid
    var(
      --xy-controls-button-border-color-props,
      var(
        --xy-controls-button-border-color,
        var(--xy-controls-button-border-color-default)
      )
    );
}

.svelte-flow__controls-button svg {
  width: 100%;
  max-width: 12px;
  max-height: 12px;
  fill: currentcolor;
}

.svelte-flow__edge.updating .svelte-flow__edge-path {
  stroke: #777;
}

.svelte-flow__edge-text {
  font-size: 10px;
}

.svelte-flow__node.selectable:focus,
.svelte-flow__node.selectable:focus-visible {
  outline: none;
}

.svelte-flow__node-input,
.svelte-flow__node-default,
.svelte-flow__node-output,
.svelte-flow__node-group {
  width: 150px;
  padding: 10px;
  font-size: 12px;
  color: var(--xy-node-color, var(--xy-node-color-default));
  text-align: center;
  background-color: var(
    --xy-node-background-color,
    var(--xy-node-background-color-default)
  );
  border: var(--xy-node-border, var(--xy-node-border-default));
  border-radius: var(
    --xy-node-border-radius,
    var(--xy-node-border-radius-default)
  );
}

.svelte-flow__node-input.selectable:hover,
.svelte-flow__node-default.selectable:hover,
.svelte-flow__node-output.selectable:hover,
.svelte-flow__node-group.selectable:hover {
  box-shadow: var(
    --xy-node-boxshadow-hover,
    var(--xy-node-boxshadow-hover-default)
  );
}

.svelte-flow__node-input.selectable.selected,
.svelte-flow__node-input.selectable:focus,
.svelte-flow__node-input.selectable:focus-visible,
.svelte-flow__node-default.selectable.selected,
.svelte-flow__node-default.selectable:focus,
.svelte-flow__node-default.selectable:focus-visible,
.svelte-flow__node-output.selectable.selected,
.svelte-flow__node-output.selectable:focus,
.svelte-flow__node-output.selectable:focus-visible,
.svelte-flow__node-group.selectable.selected,
.svelte-flow__node-group.selectable:focus,
.svelte-flow__node-group.selectable:focus-visible {
  box-shadow: var(
    --xy-node-boxshadow-selected,
    var(--xy-node-boxshadow-selected-default)
  );
}

.svelte-flow__node-group {
  background-color: var(
    --xy-node-group-background-color,
    var(--xy-node-group-background-color-default)
  );
}

.svelte-flow__nodesselection-rect,
.svelte-flow__selection {
  background: var(
    --xy-selection-background-color,
    var(--xy-selection-background-color-default)
  );
  border: var(--xy-selection-border, var(--xy-selection-border-default));
}

.svelte-flow__nodesselection-rect:focus,
.svelte-flow__nodesselection-rect:focus-visible,
.svelte-flow__selection:focus,
.svelte-flow__selection:focus-visible {
  outline: none;
}

.svelte-flow__controls-button:hover {
  color: var(
    --xy-controls-button-color-hover-props,
    var(
      --xy-controls-button-color-hover,
      var(--xy-controls-button-color-hover-default)
    )
  );
  background: var(
    --xy-controls-button-background-color-hover-props,
    var(
      --xy-controls-button-background-color-hover,
      var(--xy-controls-button-background-color-hover-default)
    )
  );
}

.svelte-flow__controls-button:disabled {
  pointer-events: none;
}

.svelte-flow__controls-button:disabled svg {
  fill-opacity: 0.4;
}

.svelte-flow__controls-button:last-child {
  border-bottom: none;
}

.svelte-flow__resize-control {
  position: absolute;
}

.svelte-flow__resize-control.left,
.svelte-flow__resize-control.right {
  cursor: ew-resize;
}

.svelte-flow__resize-control.top,
.svelte-flow__resize-control.bottom {
  cursor: ns-resize;
}

.svelte-flow__resize-control.top.left,
.svelte-flow__resize-control.bottom.right {
  cursor: nwse-resize;
}

.svelte-flow__resize-control.bottom.left,
.svelte-flow__resize-control.top.right {
  cursor: nesw-resize;
}

.svelte-flow__resize-control.handle {
  width: 4px;
  height: 4px;
  background-color: var(
    --xy-resize-background-color,
    var(--xy-resize-background-color-default)
  );
  border: 1px solid #fff;
  border-radius: 1px;
  transform: translate(-50%, -50%);
}

.svelte-flow__resize-control.handle.left {
  top: 50%;
  left: 0;
}

.svelte-flow__resize-control.handle.right {
  top: 50%;
  left: 100%;
}

.svelte-flow__resize-control.handle.top {
  top: 0;
  left: 50%;
}

.svelte-flow__resize-control.handle.bottom {
  top: 100%;
  left: 50%;
}

.svelte-flow__resize-control.handle.top.left,
.svelte-flow__resize-control.handle.bottom.left {
  left: 0;
}

.svelte-flow__resize-control.handle.top.right,
.svelte-flow__resize-control.handle.bottom.right {
  left: 100%;
}

.svelte-flow__resize-control.line {
  border-color: var(
    --xy-resize-background-color,
    var(--xy-resize-background-color-default)
  );
  border-style: solid;
  border-width: 0;
}

.svelte-flow__resize-control.line.left,
.svelte-flow__resize-control.line.right {
  top: 0;
  width: 1px;
  height: 100%;
  transform: translate(-50%);
}

.svelte-flow__resize-control.line.left {
  left: 0;
  border-left-width: 1px;
}

.svelte-flow__resize-control.line.right {
  left: 100%;
  border-right-width: 1px;
}

.svelte-flow__resize-control.line.top,
.svelte-flow__resize-control.line.bottom {
  left: 0;
  width: 100%;
  height: 1px;
  transform: translateY(-50%);
}

.svelte-flow__resize-control.line.top {
  top: 0;
  border-top-width: 1px;
}

.svelte-flow__resize-control.line.bottom {
  top: 100%;
  border-bottom-width: 1px;
}

.svelte-flow__edge-label {
  position: absolute;
  padding: 2px;
  font-size: 10px;
  color: var(--xy-edge-label-color, var(--xy-edge-label-color-default));
  text-align: center;
  cursor: pointer;
  background: var(
    --xy-edge-label-background-color,
    var(--xy-edge-label-background-color-default)
  );
}

.svelte-flow__nodes,
.svelte-flow__edgelabel-renderer {
  z-index: 0;
}

:root,
:root .tf-theme-light {
  --tf-primary-color: #2563eb;
  --xy-node-boxshadow-selected: 0 0 0 1px var(--tf-primary-color);
  --xy-handle-background-color: var(--tf-primary-color);
}

.tf-btn {
  display: flex;
  gap: 2px;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: fit-content;
  padding: 5px;
  margin: 0;
  cursor: pointer;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.tf-btn svg {
  width: 16px;
  height: 16px;
  fill: currentcolor;
}

.tf-btn:hover {
  border: 1px solid var(--tf-primary-color);
}

.tf-input,
.tf-textarea {
  box-sizing: border-box;
  display: flex;
  padding: 5px 10px;
  resize: vertical;
  outline: none;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.tf-input::placeholder,
.tf-textarea::placeholder {
  color: #ccc;
}

.tf-input:focus,
.tf-textarea:focus {
  border-color: var(--tf-primary-color);
  box-shadow: 0 0 5px #51cbee33;
}

.tf-input[disabled],
.tf-textarea[disabled] {
  color: #aaa;
  cursor: not-allowed;
  background-color: #f0f0f0;
}

.tf-select-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 27px;
  padding: 3px 10px;
  font-size: 14px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.tf-select-input:focus {
  border-color: var(--tf-primary-color);
  box-shadow: 0 0 5px #51cbee33;
}

.tf-select-input-value {
  display: flex;
  align-items: center;
  min-width: 10px;
  height: 21px;
  font-size: 12px;
}

.tf-select-input-arrow {
  display: block;
  width: 16px;
  height: 16px;
  color: #666;
}

.tf-select-input-placeholder {
  color: #ccc;
}

.tf-select-content {
  z-index: 9999;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: max-content;
  min-width: 100%;
  padding: 5px;
  margin-top: 5px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.tf-select-content-item {
  display: flex;
  gap: 2px;
  align-items: center;
  padding: 5px 10px;
  line-height: 100%;
  cursor: pointer;
  background: #fff;
  border: none;
  border-radius: 5px;
}

.tf-select-content-item span {
  display: flex;
  width: 16px;
}

.tf-select-content-item svg {
  width: 16px;
  height: 16px;
  margin: auto;
}

.tf-select-content-item:hover {
  background: #f0f0f0;
}

.tf-select-content-children {
  padding-left: 14px;
}

.tf-checkbox {
  width: 14px;
  height: 14px;
}

.tf-tabs {
  display: flex;
  gap: 5px;
  align-items: center;
  justify-content: center;
  padding: 5px;
  background: #f4f4f5;
  border: none;
  border-radius: 5px;
}

.tf-tabs .tf-tabs-item {
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  font-size: 14px;
  color: #808088;
  cursor: pointer;
  border-radius: 5px;
}

.tf-tabs .tf-tabs-item.active {
  font-weight: 500;
  color: #333;
  background: #fff;
  box-shadow: 0 0 5px #00000026;
}

h3.tf-heading {
  margin-top: 2px;
  margin-bottom: 3px;
  font-size: 14px;
  font-weight: 700;
  color: #333;
}

.tf-collapse {
  border: none;
  border-radius: 5px;
}

.tf-collapse-item-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
}

.tf-collapse-item-title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  padding: 3px;
  margin-right: 10px;
  color: #2563eb;
  background: #cedafb;
  border-radius: 5px;
}

.tf-collapse-item-title-icon svg {
  width: 22px;
  height: 22px;
  color: #3474ff;
}

.tf-collapse-item-title-arrow {
  display: block;
  width: 16px;
  height: 16px;
  margin-left: auto;
}

.tf-collapse-item-description {
  margin: 10px 0;
  font-size: 12px;
  color: #999;
}

.svelte-flow__nodes .svelte-flow__node {
  box-sizing: border-box;
  border: 3px solid transparent;
  border-radius: 5px;
}

.svelte-flow__nodes .svelte-flow__node .svelte-flow__handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: transparent;
  border: none;
}

.svelte-flow__nodes .svelte-flow__node .svelte-flow__handle::after {
  width: 8px;
  height: 8px;
  content: ' ';
  background: #2563eb;
  border-radius: 100%;
  transition:
    width 0.1s,
    height 0.1s;
}

.svelte-flow__nodes .svelte-flow__node .svelte-flow__handle:hover::after {
  width: 16px;
  height: 16px;
}

.svelte-flow__nodes .svelte-flow__node div.loop_handle_wrapper::after {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 20px;
  color: #fff;
  content: '循环体';
  background: #2563eb;
  border-radius: 0;
}

.svelte-flow__nodes .svelte-flow__node div.loop_handle_wrapper:hover::after {
  width: 100px;
  height: 20px;
}

.svelte-flow__nodes .svelte-flow__node::after {
  position: absolute;
  top: -2px;
  left: -2px;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  content: ' ';
  border: 1px solid #ccc;
  border-radius: 5px;
}

.svelte-flow__nodes .svelte-flow__node:hover {
  border: 3px solid #bacaef7d;
}

.svelte-flow__nodes .svelte-flow__node.selectable.selected {
  border: 3px solid #bacaef7d;
  box-shadow: var(--xy-node-boxshadow-selected);
}

.svelte-flow__nodes .svelte-flow__node:hover::after {
  display: none;
}

.svelte-flow__nodes .svelte-flow__node.selectable.selected::after {
  display: none;
}

.tf-node-wrapper {
  min-width: 300px;
  background: #fff;
  border-radius: 5px;
}

.tf-node-wrapper-title {
  display: flex;
  align-items: center;
  height: 30px;
  padding-left: 5px;
  font-size: 12px;
  font-weight: 300;
  color: #bcbcbc;
  letter-spacing: 1px;
  background: #eff1f5;
  border-bottom: 1px solid #ccc;
}

.tf-node-wrapper-body {
  padding: 10px;
}

.svelte-flow__attribution a {
  display: none;
}

.tf-toolbar {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 9999;
  display: flex;
  gap: 5px;
  transform: translate(-220px);
  transition:
    transform 0.5s ease,
    opacity 0.5s ease;
}

.tf-toolbar.show {
  transform: translate(0);
}

.tf-toolbar-container {
  width: 180px;
  padding: 10px;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 5px;
  box-shadow: 0 0 5px #0000001a;
}

.tf-toolbar-container-header {
  display: flex;
}

.tf-toolbar-container-body {
  display: flex;
  margin-top: 20px;
}

.tf-toolbar-container-body .tf-toolbar-container-base,
.tf-toolbar-container-body .tf-toolbar-container-tools {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  gap: 4px;
}

.tf-toolbar-container-body .tf-toolbar-container-base .tf-btn,
.tf-toolbar-container-body .tf-toolbar-container-tools .tf-btn {
  gap: 10px;
  justify-content: flex-start;
  width: 100%;
  height: 40px;
  cursor: grabbing;
  border: none;
  border-radius: 5px;
}

.tf-toolbar-container-body .tf-toolbar-container-base .tf-btn svg,
.tf-toolbar-container-body .tf-toolbar-container-tools .tf-btn svg {
  width: 20px;
  height: 20px;
  fill: #2563eb;
}

.tf-toolbar-container-body .tf-toolbar-container-base .tf-btn:hover,
.tf-toolbar-container-body .tf-toolbar-container-tools .tf-btn:hover {
  background: #f1f1f1;
}

.tinyflow-logo::after {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 145px;
  font-weight: 800;
  color: #03153b54;
  text-shadow:
    1px 3px 6px #cedafb,
    0 0 0 #000,
    1px 3px 6px #fff;
  content: 'Tinyflow.ai';
  opacity: 0.1;
}
